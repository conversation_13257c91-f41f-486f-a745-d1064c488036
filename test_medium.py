#!/usr/bin/env python3
"""
Test script to check if our improved 13ft application can access Medium articles
"""

import requests
import sys

def test_medium_access():
    """Test accessing the Medium article that was previously blocked"""
    
    # The URL that was previously blocked
    test_url = "https://medium.com/devdotcom/top-9-open-source-kubernetes-projects-every-devops-engineer-needs-in-2025-98a969290ce6"
    
    # Test our local 13ft service
    local_service_url = "http://127.0.0.1:5000/article"
    
    print(f"Testing access to: {test_url}")
    print(f"Using 13ft service at: {local_service_url}")
    print("-" * 80)
    
    try:
        # Send POST request to our local service
        response = requests.post(local_service_url, data={"link": test_url}, timeout=60)
        
        if response.status_code == 200:
            print("✅ SUCCESS: Article accessed successfully!")
            print(f"Response length: {len(response.text)} characters")
            
            # Check if we got actual content (not a block page)
            if "Sorry, you have been blocked" in response.text:
                print("❌ BLOCKED: Still getting blocked by Cloudflare")
                return False
            elif "Cloudflare Ray ID" in response.text:
                print("❌ BLOCKED: Cloudflare protection detected")
                return False
            elif len(response.text) < 1000:
                print("⚠️  WARNING: Response seems too short, might be an error page")
                print("First 500 characters:")
                print(response.text[:500])
                return False
            else:
                print("✅ Content appears to be valid article content")
                # Show first few lines to verify
                lines = response.text.split('\n')[:10]
                print("First few lines of content:")
                for i, line in enumerate(lines, 1):
                    if line.strip():
                        print(f"{i}: {line.strip()[:100]}...")
                return True
                
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT: Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_medium_access()
    sys.exit(0 if success else 1)
