name: Build

# Controls when the workflow will run
on:
  workflow_dispatch:
  push:
    branches:
      - 'main'
      - 'dev'
    tags:
      - 'v*.*.*'
  pull_request:
    branches:
      - 'main'
      - 'dev'

# permissions are needed if pushing to ghcr.io
permissions: 
  packages: write

jobs:
  build:
    name: "Build and release"
    runs-on: ubuntu-latest
    steps:
      # Get the repository's code
      - name: ⬇️ checkout
        uses: actions/checkout@v2

      # https://github.com/docker/setup-qemu-action
      - name: ⚙ set up qemu
        uses: docker/setup-qemu-action@v1

      # https://github.com/docker/setup-buildx-action
      - name: ⚙ set up docker buildx
        id: buildx
        uses: docker/setup-buildx-action@v1
      
      - name: 👤 login to ghcr
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏷 docker meta
        id: docker_meta # you'll use this in the next step
        uses: docker/metadata-action@v3
        with:
          # list of Docker images to use as base name for tags
          images: |
            ghcr.io/${{ github.repository_owner }}/13ft
          # Docker tags based on the following events/attributes
          tags: |
            type=schedule
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha
  
      - name: 📦 build and ⬆ push
        uses: docker/build-push-action@v2
        with:
          context: .
          platforms: linux/amd64,linux/arm/v7,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.docker_meta.outputs.tags }}
          labels: ${{ steps.docker_meta.outputs.labels }}
