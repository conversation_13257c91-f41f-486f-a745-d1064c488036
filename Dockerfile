FROM python:3.9.18-alpine

# Generic labels
LABEL maintainer="Aria<PERSON> <<EMAIL>>"
LABEL version="0.3.5-improved"
LABEL description="Improved 13ft with enhanced bot detection bypass"
LABEL url="https://github.com/wasi-master/13ft/"
LABEL documentation="https://github.com/wasi-master/13ft/blob/main/README.md"

# OCI compliant labels
LABEL org.opencontainers.image.source="https://github.com/wasi-master/13ft"
LABEL org.opencontainers.image.authors="Arian Mollik <PERSON>"
LABEL org.opencontainers.image.created="2025-07-19T21:15:00Z"
LABEL org.opencontainers.image.version="0.3.5-improved"
LABEL org.opencontainers.image.url="https://github.com/wasi-master/13ft/"
LABEL org.opencontainers.image.source="https://github.com/wasi-master/13ft/"
LABEL org.opencontainers.image.description="Improved 13ft with enhanced bot detection bypass"
LABEL org.opencontainers.image.documentation="https://github.com/wasi-master/13ft/blob/main/README.md"
LABEL org.opencontainers.image.licenses=MIT

COPY . .
RUN pip install -r requirements.txt
WORKDIR /app
EXPOSE 5000
ENTRYPOINT [ "python" ]
CMD [ "portable.py" ] 