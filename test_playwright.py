#!/usr/bin/env python3

import asyncio
from playwright.async_api import async_playwright
import time

async def test_13ft_service():
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print("Opening 13ft service...")
            await page.goto("http://127.0.0.1:8080")
            
            # Wait for page to load
            await page.wait_for_load_state('networkidle')
            
            # Take a screenshot of the initial page
            await page.screenshot(path="13ft_initial.png")
            print("Initial page loaded, screenshot saved as 13ft_initial.png")
            
            # Find the URL input field and paste the Medium URL
            medium_url = "https://medium.com/devdotcom/top-9-open-source-kubernetes-projects-every-devops-engineer-needs-in-2025-98a969290ce6"
            
            print(f"Entering URL: {medium_url}")
            
            # Look for input field - try different selectors
            url_input = None
            selectors_to_try = [
                'input[type="url"]',
                'input[name="url"]', 
                'input[placeholder*="URL"]',
                'input[placeholder*="url"]',
                'input[placeholder*="link"]',
                'input[type="text"]',
                'textarea'
            ]
            
            for selector in selectors_to_try:
                try:
                    url_input = await page.wait_for_selector(selector, timeout=2000)
                    if url_input:
                        print(f"Found input field with selector: {selector}")
                        break
                except:
                    continue
            
            if not url_input:
                print("Could not find URL input field. Taking screenshot...")
                await page.screenshot(path="13ft_no_input.png")
                
                # Print page content for debugging
                content = await page.content()
                print("Page HTML content:")
                print(content[:1000] + "..." if len(content) > 1000 else content)
                return
            
            # Clear and fill the input
            await url_input.click()
            await url_input.fill(medium_url)
            
            # Look for submit button
            submit_button = None
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Submit")',
                'button:has-text("Get")',
                'button:has-text("Fetch")',
                'button',
                '.btn'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = await page.wait_for_selector(selector, timeout=2000)
                    if submit_button:
                        print(f"Found submit button with selector: {selector}")
                        break
                except:
                    continue
            
            if not submit_button:
                print("Could not find submit button. Trying Enter key...")
                await url_input.press('Enter')
            else:
                print("Clicking submit button...")
                await submit_button.click()
            
            # Wait for response
            print("Waiting for response...")
            await page.wait_for_load_state('networkidle', timeout=30000)
            
            # Take screenshot of result
            await page.screenshot(path="13ft_result.png")
            print("Result screenshot saved as 13ft_result.png")
            
            # Check if there's an error message
            error_selectors = [
                '.error',
                '.alert',
                '[class*="error"]',
                '[class*="fail"]',
                'p:has-text("403")',
                'p:has-text("Error")',
                'p:has-text("Failed")'
            ]
            
            error_found = False
            for selector in error_selectors:
                try:
                    error_element = await page.wait_for_selector(selector, timeout=1000)
                    if error_element:
                        error_text = await error_element.text_content()
                        print(f"Error found: {error_text}")
                        error_found = True
                        break
                except:
                    continue
            
            if not error_found:
                # Check if article content is present
                content_selectors = [
                    'article',
                    '.article',
                    '[class*="content"]',
                    'main',
                    '.post'
                ]
                
                content_found = False
                for selector in content_selectors:
                    try:
                        content_element = await page.wait_for_selector(selector, timeout=2000)
                        if content_element:
                            content_text = await content_element.text_content()
                            if len(content_text.strip()) > 100:  # Substantial content
                                print(f"Article content found! Length: {len(content_text)} characters")
                                print(f"First 200 chars: {content_text[:200]}...")
                                content_found = True
                                break
                    except:
                        continue
                
                if not content_found:
                    print("No substantial article content found. Checking page text...")
                    page_text = await page.text_content('body')
                    print(f"Page text length: {len(page_text)} characters")
                    print(f"First 500 chars: {page_text[:500]}...")
            
            # Wait a bit for user to see the result
            await asyncio.sleep(3)
            
        except Exception as e:
            print(f"Error during test: {e}")
            await page.screenshot(path="13ft_error.png")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_13ft_service())
